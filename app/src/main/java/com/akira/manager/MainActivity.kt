package com.akira.manager

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.PowerManager
import android.provider.Settings
import android.webkit.MimeTypeMap
import android.webkit.URLUtil
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.remember
import com.akira.manager.data.service.DownloadService
import com.akira.manager.ui.theme.MDMTheme
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.view.WindowCompat
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import com.akira.manager.data.local.SettingsManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.lifecycle.viewmodel.compose.viewModel

import com.akira.manager.ui.screen.MainScreen
import com.akira.manager.ui.screen.SettingsScreen
import java.io.Serializable
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.runBlocking
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.with
import com.akira.manager.ui.viewmodel.DownloadViewModel
import androidx.compose.runtime.collectAsState
import androidx.navigation.navArgument
import android.widget.Toast
import android.content.pm.ShortcutManager
import android.content.pm.ShortcutInfo
import android.graphics.drawable.Icon
import kotlinx.coroutines.delay
import com.akira.manager.sharing.HandleDownloadShareActivity
import android.content.SharedPreferences
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.dp
import com.akira.manager.ui.theme.getDialogBackgroundColor
import androidx.compose.material3.ButtonDefaults
import android.util.Log

class MainActivity : ComponentActivity() {
    companion object {
        private const val ACTION_DOWNLOAD = "android.intent.action.DOWNLOAD"
    }

    // Set to track already processed download URLs during a single app launch
    private val processedUrls = mutableSetOf<String>()

    // State to hold pending action from share activities
    private var pendingShareAction by mutableStateOf<String?>(null)
    private var pendingShareUrl by mutableStateOf<String?>(null)

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            // Permissions granted, proceed with app
            checkBatteryOptimization()
        } else {
            // Handle permission denied
            showPermissionDeniedDialog()
        }
    }

    private val requestNotificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            checkBatteryOptimization()
        } else {
            // Optionally show a message about the importance of notifications
            showNotificationPermissionDeniedDialog()
        }
    }

    private val openSettingsLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        // Check permissions again after returning from settings
        checkAndRequestPermissions()
    }

    private val batteryOptimizationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        // Continue with app initialization after battery optimization dialog
        initializeApp()
    }

    private lateinit var shortcutManager: ShortcutManager

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen before super.onCreate()
        installSplashScreen().apply {
            // Keep the splash screen visible until the app is fully loaded
            setKeepOnScreenCondition { false }
        }
        
        super.onCreate(savedInstanceState)

        WindowCompat.setDecorFitsSystemWindows(window, false)
        enableEdgeToEdge()

        // Initialize ShortcutManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            shortcutManager = getSystemService(ShortcutManager::class.java)
        }
        
        // Process the initial intent
        processIntent(intent)
        
        // Request permissions if needed
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestNotificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        } else {
            checkBatteryOptimization()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // Process the new intent when the activity is re-launched
        setIntent(intent) // Important: Update the activity's intent reference
        processIntent(intent)
    }
    
    private fun processIntent(intent: Intent?) {
        if (intent == null) return

        var potentialUrl: String? = null
        var isDownloadIntent = false
        var fileName: String? = null
        var mimeType: String? = null

        // Add debug logging for intent processing
        Log.d("MainActivity", "Processing intent: ${intent.action}, extras: ${intent.extras?.keySet()?.joinToString()}")
        
        // Check if this is a direct share from Firefox or if it's already been processed by our share handlers
        val alreadyProcessed = intent.getBooleanExtra(HandleDownloadShareActivity.PROCESSED_BY_SHARE_HANDLER, false)
        
        // Get potential URL early for duplicate detection
        if (intent.action == Intent.ACTION_SEND && intent.type == "text/plain") {
            potentialUrl = intent.getStringExtra(Intent.EXTRA_TEXT)
        } else if (intent.action == HandleDownloadShareActivity.ACTION_SHARE_DOWNLOAD) {
            potentialUrl = intent.getStringExtra(HandleDownloadShareActivity.SHARED_URL)
        } else if (intent.action in listOf(Intent.ACTION_VIEW, ACTION_DOWNLOAD, 
                                          "com.android.browser.DOWNLOAD", 
                                          "mozilla.components.feature.downloads.DOWNLOAD_MANAGER")) {
            potentialUrl = intent.dataString
        }
        
        // Skip processing if we've already handled this URL during this app session
        if (!potentialUrl.isNullOrBlank() && processedUrls.contains(potentialUrl)) {
            Log.d("MainActivity", "Skipping already processed URL during this session: $potentialUrl")
            return
        }

        when (intent.action) {
            // Our custom share actions
            HandleDownloadShareActivity.ACTION_SHARE_DOWNLOAD -> {
                potentialUrl = intent.getStringExtra(HandleDownloadShareActivity.SHARED_URL)
                isDownloadIntent = true
                Log.d("MainActivity", "Processing download share action: $potentialUrl")
            }
            HandleBrowserShareActivity.ACTION_SHARE_BROWSER -> {
                potentialUrl = intent.getStringExtra(HandleDownloadShareActivity.SHARED_URL)
                // Set action explicitly for browser navigation later
                if (!potentialUrl.isNullOrBlank()) {
                    pendingShareAction = intent.action
                    pendingShareUrl = potentialUrl
                }
                // Return early as this is not a download intent
                return
            }

            // Standard ACTION_SEND (likely from Firefox share)
            Intent.ACTION_SEND -> {
                // Only process raw ACTION_SEND intents if they haven't been processed by our share handlers
                if (intent.type == "text/plain" && !alreadyProcessed) {
                    isDownloadIntent = true
                    Log.d("MainActivity", "Processing direct ACTION_SEND intent: $potentialUrl")
                } else {
                    Log.d("MainActivity", "Skipping already processed ACTION_SEND intent")
                }
            }

            // Standard ACTION_VIEW or Firefox-specific DOWNLOAD actions
            Intent.ACTION_VIEW, ACTION_DOWNLOAD, "com.android.browser.DOWNLOAD", "mozilla.components.feature.downloads.DOWNLOAD_MANAGER" -> {
                isDownloadIntent = true
                
                // Try to get filename and mime type from intent extras
                fileName = intent.getStringExtra(Intent.EXTRA_TITLE)
                    ?: intent.getStringExtra("android.intent.extra.TITLE")
                    ?: intent.getStringExtra("android.intent.extra.SUBJECT")
                
                mimeType = intent.type
                    ?: intent.getStringExtra("android.intent.extra.MIME_TYPE")
            }
        }

        // If we identified a URL from a download-related intent, set the pending state
        if (isDownloadIntent && !potentialUrl.isNullOrBlank()) {
            pendingShareAction = HandleDownloadShareActivity.ACTION_SHARE_DOWNLOAD // Treat as download
            pendingShareUrl = potentialUrl
            
            // For intents that come with file metadata (e.g., from Firefox), we handle them directly
            // and add them to processedUrls to prevent the LaunchedEffect from re-triggering.
            if (fileName != null || mimeType != null) {
                 if (processedUrls.contains(potentialUrl)) {
                     Log.d("MainActivity", "Skipping already processed direct download: $potentialUrl")
                     return // Already handled
                 }
                processedUrls.add(potentialUrl)
                val downloadViewModel = (application as MaterialManagerApp).downloadViewModel
                downloadViewModel.addExternalDownload(
                    url = potentialUrl,
                    fileName = fileName ?: URLUtil.guessFileName(potentialUrl, null, null),
                    mimeType = mimeType ?: "application/octet-stream",
                    source = "firefox"
                )
            }
        }
    }

    private fun handleDownloadUrl(url: String, source: String) {
        try {
            // Check if we've already processed this URL during this app session
            if (processedUrls.contains(url)) {
                Log.d("MainActivity", "Skipping already handled URL in handleDownloadUrl: $url")
                return
            }
            
            // Mark URL as processed
            processedUrls.add(url)
            Log.d("MainActivity", "Handling download URL: $url")
            
            // Extract filename from URL
            val isContentUri = url.startsWith("content://") || url.startsWith("file://")
            
            // Handle special case for content or file URIs (often from Firefox on older Android versions)
            if (isContentUri) {
                try {
                    val uri = Uri.parse(url)
                    
                    // Get file details from ContentResolver
                    val cursor = contentResolver.query(uri, null, null, null, null)
                    cursor?.use { c ->
                        if (c.moveToFirst()) {
                            // Get MIME type and filename from content resolver
                            val nameIndex = c.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                            var fileName = if (nameIndex != -1) {
                                c.getString(nameIndex)
                            } else {
                                "download_${System.currentTimeMillis()}"
                            }
                            
                            // Get MIME type
                            var mimeType = contentResolver.getType(uri) ?: "application/octet-stream"
                            
                            // If we have a valid file name and mime type, add it to downloads
                            if (fileName.isNotEmpty()) {
                                // Ensure we have an extension
                                if (!fileName.contains(".")) {
                                    val extension = MimeTypeMap.getSingleton()
                                        .getExtensionFromMimeType(mimeType)
                                    if (extension != null) {
                                        fileName = "$fileName.$extension"
                                    }
                                }
                                
                                // Use the ViewModel to add the download to ensure consistency with UI
                                val downloadViewModel = (application as MaterialManagerApp).downloadViewModel
                                downloadViewModel.addExternalDownload(url, fileName, mimeType, "firefox")
                                return
                            }
                        }
                    }
                } catch (e: Exception) {
                    // Fall back to regular URL handling if content resolver fails
                    e.printStackTrace()
                }
            }
            
            // Regular URL handling for HTTP/HTTPS URLs
            val fileName = URLUtil.guessFileName(url, null, null)
            val mimeType = MimeTypeMap.getSingleton()
                .getMimeTypeFromExtension(fileName.substringAfterLast("."))
                ?: "application/octet-stream"

            // Use the ViewModel to add the download to ensure consistency with UI
            val downloadViewModel = (application as MaterialManagerApp).downloadViewModel
            downloadViewModel.addExternalDownload(url, fileName, mimeType, source)
        } catch (e: Exception) {
            // Handle error
            e.printStackTrace()
            
            // Show toast with error message
            Toast.makeText(
                this,
                "Failed to add download: ${e.message}",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun checkAndRequestPermissions() {
        // For Android 10 (API level 29) and above, we don't need storage permissions
        // for accessing Download directory
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            initializeApp()
            return
        }

        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        if (permissions.all { checkSelfPermission(it) == android.content.pm.PackageManager.PERMISSION_GRANTED }) {
            initializeApp()
        } else {
            requestPermissionLauncher.launch(permissions)
        }
    }

    private fun showStoragePermissionDialog() {
        // Only show settings dialog if we're on Android 9 or below
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", packageName, null)
            }
            openSettingsLauncher.launch(intent)
        } else {
            initializeApp()
        }
    }

    private fun checkBatteryOptimization() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && 
            !powerManager.isIgnoringBatteryOptimizations(packageName)) {
            // Show dialog asking user to disable battery optimization
            showBatteryOptimizationDialog()
        } else {
            // Battery optimization already disabled or not needed
            initializeApp()
        }
    }

    private fun showBatteryOptimizationDialog() {
        val batteryIntent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
            data = Uri.parse("package:$packageName")
        }
        
        // We have to create the dialog in the Compose setContent block, but we can launch the intent here
        batteryOptimizationLauncher.launch(batteryIntent)
    }

    @OptIn(ExperimentalAnimationApi::class)
    private fun initializeApp() {
        val settingsManager = SettingsManager(this)
        
        // Preload AMOLED theme preference to avoid theme flicker
        var isAmoledDarkMode = runBlocking { 
            settingsManager.isAmoledDarkMode.firstOrNull() ?: false 
        }
        
        setContent {
            val systemUiController = rememberSystemUiController()
            val darkTheme = isSystemInDarkTheme()
            val navController = rememberNavController()
            val downloadViewModel: DownloadViewModel = viewModel()
            
            val currentAmoledMode = settingsManager.isAmoledDarkMode.collectAsState(initial = isAmoledDarkMode).value
            
            MDMTheme(
                darkTheme = darkTheme,
                amoledDarkMode = currentAmoledMode
            ) {
                // Capture the current theme's color scheme
                val currentScheme = MaterialTheme.colorScheme

                // Accessibility Dialog State - now inside MDMTheme
                var showAccessibilityDialog by remember { mutableStateOf(shouldShowAccessibilityDialog() && !isAccessibilityServiceEnabled()) }
                
                if (showAccessibilityDialog) {
                    AlertDialog(
                        onDismissRequest = {
                            showAccessibilityDialog = false
                            setAccessibilityDialogShown()
                        },
                        title = { Text("Enable Accessibility Service", style = MaterialTheme.typography.headlineSmall.copy(color = currentScheme.onSurface)) },
                        text = { Text("Only give the app accessibility permission if the app is killed automatically. You don't need to give this permission if the app works properly in the background.", style = MaterialTheme.typography.bodyMedium.copy(color = currentScheme.onSurfaceVariant)) },
                        confirmButton = {
                            Button(
                                onClick = {
                                    setAccessibilityDialogShown()
                                    showAccessibilityDialog = false
                                    val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                                    startActivity(intent)
                                },
                                shape = RoundedCornerShape(20.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = currentScheme.primary,
                                    contentColor = currentScheme.onPrimary
                                )
                            ) {
                                Text("Open Settings")
                            }
                        },
                        dismissButton = {
                            Button(
                                onClick = {
                                    setAccessibilityDialogShown()
                                    showAccessibilityDialog = false
                                },
                                shape = RoundedCornerShape(20.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = currentScheme.surfaceVariant,
                                    contentColor = currentScheme.onSurfaceVariant
                                )
                            ) {
                                Text("Cancel")
                            }
                        },
                        containerColor = getDialogBackgroundColor(currentScheme),
                        titleContentColor = currentScheme.onSurface,
                        textContentColor = currentScheme.onSurfaceVariant,
                        shape = RoundedCornerShape(28.dp)
                    )
                }

                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // Handle pending share actions *after* NavController is initialized and theme is applied
                    LaunchedEffect(pendingShareAction, pendingShareUrl) {
                        val action = pendingShareAction
                        val url = pendingShareUrl
                        if (action != null && url != null) {
                            Log.d("MainActivity", "Processing pending share action: $action for URL: $url")
                            // Clear early to prevent duplicate processing
                            pendingShareAction = null
                            pendingShareUrl = null
                            
                            when (action) {
                                HandleDownloadShareActivity.ACTION_SHARE_DOWNLOAD -> {
                                    navController.navigate("main") {
                                        popUpTo(navController.graph.startDestinationId) { inclusive = false }
                                        launchSingleTop = true
                                    }
                                    handleDownloadUrl(url, "share")
                                }
                                HandleBrowserShareActivity.ACTION_SHARE_BROWSER -> {
                                    val encodedUrl = Uri.encode(url)
                                    navController.navigate("browser?url=$encodedUrl") {
                                        popUpTo(navController.graph.startDestinationId) { inclusive = false }
                                        launchSingleTop = true
                                    }
                                }
                            }
                        }
                    }
                    
                    // Handle shortcut intent
                    LaunchedEffect(Unit) {                
                        val route = intent.getStringExtra("route")
                        val action = intent.getStringExtra("action")
                        
                        when {
                            route == "browser" && action == "download" -> {
                                navController.navigate("main") {
                                    popUpTo(navController.graph.startDestinationId) {
                                        inclusive = false
                                    }
                                }
                                delay(100)
                                downloadViewModel.mainScreenState.showAddDownloadDialog.value = true
                            }
                        }
                    }
                    
                    NavHost(
                        navController = navController,
                        startDestination = "main"
                    ) {
                        composable(
                            route = "main",
                            enterTransition = {
                                fadeIn(animationSpec = tween(300))
                            },
                            exitTransition = {
                                fadeOut(animationSpec = tween(300))
                            }
                        ) {
                            MainScreen(
                                viewModel = downloadViewModel,
                                onSettingsClick = {
                                    navController.navigate("settings")
                                }
                            )
                        }

                        composable(
                            route = "settings",
                            enterTransition = {
                                fadeIn(animationSpec = tween(300))
                            },
                            exitTransition = {
                                fadeOut(animationSpec = tween(300))
                            }
                        ) {
                            SettingsScreen(
                                onBackClick = {
                                    navController.navigateUp()
                                },
                                settingsManager = settingsManager
                            )
                        }


                    }
                }
            }
        }
    }

    private fun showPermissionDeniedDialog() {
        // Show settings dialog for storage permissions
        showStoragePermissionDialog()
    }

    private fun showNotificationPermissionDeniedDialog() {
        // For notification permission, we can still proceed with the app
        // but inform the user about the importance of notifications
        checkBatteryOptimization()
    }

    private fun shouldShowAccessibilityDialog(): Boolean {
        val prefs: SharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean("accessibility_dialog_shown", false)
    }

    private fun setAccessibilityDialogShown() {
        val prefs: SharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("accessibility_dialog_shown", true).apply()
    }

    private fun isAccessibilityServiceEnabled(): Boolean {
        val enabledServices = Settings.Secure.getString(
            contentResolver,
            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        ) ?: return false
        return enabledServices.split(':').any { it.contains(packageName) }
    }
}
