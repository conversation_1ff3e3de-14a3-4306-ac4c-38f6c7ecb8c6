package com.akira.manager.ui.screen

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.URLUtil
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.widget.Toast
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.activity.compose.BackHandler
import androidx.compose.runtime.LaunchedEffect // Added
import com.akira.manager.ui.viewmodel.DownloadViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.URI
import java.net.URISyntaxException
import android.content.Intent
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.foundation.Image
import androidx.compose.material3.LocalContentColor
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextAlign
import com.akira.manager.ui.theme.getUrlBarBackgroundColor
import com.akira.manager.ui.theme.getIconTint
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.ui.graphics.luminance
import com.akira.manager.util.adblock.AdBlockManager
import com.akira.manager.util.adblock.AdBlockStats
import androidx.compose.ui.unit.DpOffset
import com.akira.manager.data.local.SettingsManager
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.material.ripple.rememberRipple
import java.nio.charset.StandardCharsets

data class BrowserTab(
    val id: String = java.util.UUID.randomUUID().toString(),
    var url: String = "https://web.tabliss.io/",
    var title: String = "New Tab",
    var favicon: Bitmap? = null,
    var webView: WebView? = null,
    var errorState: WebViewError? = null,
    var isNewTab: Boolean = false,  // Add a flag to track if this is a newly created tab
    var isTabSwitched: Boolean = false  // Add a flag to track if this tab was just switched to
)

data class WebViewError(
    val errorCode: Int,
    val description: String?,
    val failingUrl: String?
)

/**
 * Handles URL schemes that WebView cannot handle natively
 * Returns true if the URL was handled externally, false if WebView should handle it
 */
private fun handleUrlScheme(url: String?, context: android.content.Context): Boolean {
    if (url.isNullOrBlank()) return false

    return try {
        val uri = android.net.Uri.parse(url)
        val scheme = uri.scheme?.lowercase()

        // Log for debugging
        android.util.Log.d("URLSchemeHandler", "Processing URL: $url with scheme: $scheme")

        when {
            // Allow standard web schemes - let WebView handle these
            scheme == "http" || scheme == "https" || scheme == "file" -> {
                android.util.Log.d("URLSchemeHandler", "Standard web URL, letting WebView handle: $url")
                false
            }

            // Handle data URLs (base64 encoded content) - let WebView handle these
            scheme == "data" -> false

            // Handle about: URLs - let WebView handle these
            scheme == "about" -> false

            // Handle blob: URLs - let WebView handle these
            scheme == "blob" -> false

            // Handle intent:// URLs (Android app links) - be very specific
            scheme == "intent" && url.startsWith("intent://") -> {
                try {
                    val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)

                    // Try to resolve the intent first
                    val resolveInfo = context.packageManager.resolveActivity(intent, 0)
                    if (resolveInfo != null) {
                        context.startActivity(intent)
                        return true
                    }

                    // If no app can handle it, try the fallback URL first
                    val fallbackUrl = intent.getStringExtra("browser_fallback_url")
                    if (!fallbackUrl.isNullOrBlank() && (fallbackUrl.startsWith("http://") || fallbackUrl.startsWith("https://"))) {
                        // Let WebView load the fallback URL instead of redirecting
                        return false
                    }

                    // Only try market URL if package is explicitly specified and no fallback exists
                    val packageName = intent.getStringExtra("package") ?: intent.`package`
                    if (!packageName.isNullOrBlank() && fallbackUrl.isNullOrBlank()) {
                        val marketIntent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse("market://details?id=$packageName"))
                        if (context.packageManager.resolveActivity(marketIntent, 0) != null) {
                            context.startActivity(marketIntent)
                            return true
                        }

                        // Fallback to Play Store web only if no other options
                        val playStoreIntent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse("https://play.google.com/store/apps/details?id=$packageName"))
                        context.startActivity(playStoreIntent)
                        return true
                    }

                    // If we can't handle it, let WebView try (it will show an error if needed)
                    return false
                } catch (e: Exception) {
                    // If parsing fails, let WebView handle it
                    return false
                }
            }

            // Handle market:// URLs (Google Play Store) - be very specific
            scheme == "market" && url.startsWith("market://") -> {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    if (context.packageManager.resolveActivity(intent, 0) != null) {
                        context.startActivity(intent)
                        return true
                    }

                    // Fallback to web version only if Play Store app is not available
                    val packageName = uri.getQueryParameter("id")
                    if (!packageName.isNullOrBlank()) {
                        val webIntent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse("https://play.google.com/store/apps/details?id=$packageName"))
                        context.startActivity(webIntent)
                        return true
                    }
                    return false
                } catch (e: Exception) {
                    return false
                }
            }

            // Handle tel:// URLs (phone calls)
            scheme == "tel" -> {
                try {
                    val intent = Intent(Intent.ACTION_DIAL, uri)
                    context.startActivity(intent)
                    return true
                } catch (e: Exception) {
                    return true
                }
            }

            // Handle mailto:// URLs (email)
            scheme == "mailto" -> {
                try {
                    val intent = Intent(Intent.ACTION_SENDTO, uri)
                    context.startActivity(intent)
                    return true
                } catch (e: Exception) {
                    return true
                }
            }

            // Handle sms:// URLs (SMS)
            scheme == "sms" || scheme == "smsto" -> {
                try {
                    val intent = Intent(Intent.ACTION_SENDTO, uri)
                    context.startActivity(intent)
                    return true
                } catch (e: Exception) {
                    return true
                }
            }

            // Handle geo:// URLs (maps)
            scheme == "geo" -> {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    context.startActivity(intent)
                    return true
                } catch (e: Exception) {
                    return true
                }
            }

            // Handle whatsapp:// URLs
            scheme == "whatsapp" -> {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    context.startActivity(intent)
                    return true
                } catch (e: Exception) {
                    return true
                }
            }

            // Handle other app-specific schemes - be conservative
            else -> {
                // Only handle schemes that are clearly not web URLs
                if (scheme != null && scheme.length > 1 && !scheme.contains(".") && !scheme.contains("/")) {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        val resolveInfo = context.packageManager.resolveActivity(intent, 0)
                        if (resolveInfo != null) {
                            // Check if it's not just the browser handling it
                            val packageName = resolveInfo.activityInfo.packageName
                            if (packageName != context.packageName) {
                                context.startActivity(intent)
                                return true
                            }
                        }
                        // If no specific app can handle it, let WebView try
                        return false
                    } catch (e: Exception) {
                        // If there's an error, let WebView handle it
                        return false
                    }
                } else {
                    // If the scheme looks like it might be a web URL variant, let WebView handle it
                    return false
                }
            }
        }
    } catch (e: Exception) {
        // If there's any error parsing the URL, let WebView handle it
        false
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun BrowserScreen(
    viewModel: DownloadViewModel,
    initialUrl: String = "https://web.tabliss.io/",
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val settingsManager = remember { SettingsManager(context) }
    
    // Initialize AdBlockManager
    val adBlockManager = remember { AdBlockManager(context) }
    
    // Ad blocker state
    var showBrowserMenu by remember { mutableStateOf(false) }
    val isAdBlockingEnabled by settingsManager.isAdBlockingEnabled.collectAsState(initial = true)
    
    // Collect ad blocking stats
    val blockedAdsCount by AdBlockStats.blockedCount.collectAsState()
    
    // Run initialization on first composition
    LaunchedEffect(Unit) {
        adBlockManager.initialize()
    }
    
    // Update ad blocker enabled state when the user toggles it
    LaunchedEffect(isAdBlockingEnabled) {
        adBlockManager.setEnabled(isAdBlockingEnabled)
    }
    
    // Tab management
    val tabs = remember { mutableStateListOf(BrowserTab(url = initialUrl)) }
    var currentTabIndex by remember { mutableStateOf(0) }
    var showTabsOverview by remember { mutableStateOf(false) }
    
    // Download interception state
    var showAddDownloadDialog by remember { mutableStateOf(false) }
    var interceptedDownloadUrl by remember { mutableStateOf("") }
    var isProcessingDownload by remember { mutableStateOf(false) }
    
    // Address bar states - Removing unused variables
    var isLoading by remember { mutableStateOf(false) }
    var loadingProgress by remember { mutableStateOf(0f) }
    
    // Navigation states
    var canGoBack by remember { mutableStateOf(false) }
    var canGoForward by remember { mutableStateOf(false) }
    
    // Bottom bar flash animation state
    var isBottomBarFlashing by remember { mutableStateOf(false) }
    
    // URL bar expansion state
    var isUrlBarExpanded by remember { mutableStateOf(false) }
    
    // Current tab reference
    val currentTab = if (tabs.isNotEmpty() && currentTabIndex < tabs.size) {
        tabs[currentTabIndex]
    } else {
        null
    }
    
    // Handle loading URL function - Modified to remove url bar updates
    val loadUrl: (String, Int) -> Unit = { url, tabIndex ->
        if (tabIndex < tabs.size) {
            val tab = tabs[tabIndex]
            
            // Set loading state immediately to provide feedback
            isLoading = true
            loadingProgress = 0.1f
            
            // Reset ad block stats for new page navigation
            AdBlockStats.reset()
            
            // Process URL with better error handling
            val correctedUrl = try {
                if (URLUtil.isNetworkUrl(url)) {
                    url
                } else if (url.contains(".") && !url.contains(" ")) {
                    "https://$url"
                } else {
                    "https://www.google.com/search?q=${url.trim()}"
                }
            } catch (e: Exception) {
                // Fallback to search if URL processing fails
                "https://www.google.com/search?q=${url.trim()}"
            }
            
            // Only load the URL if it's not blank
            if (correctedUrl.isNotBlank()) {
                try {
                    // Reset any flags that might prevent loading
                    tab.isNewTab = false
                    tab.isTabSwitched = false
                    
                    // Update the tab URL before loading to ensure consistency
                    tab.url = correctedUrl
                    
                    // Use a coroutine to load the URL
                    coroutineScope.launch {
                        // Load the URL
                        tab.webView?.loadUrl(correctedUrl)
                    }
                } catch (e: Exception) {
                    // Reset loading state if an exception occurs
                    isLoading = false
                }
            } else {
                isLoading = false
            }
            
            // Clear focus and hide keyboard
            focusManager.clearFocus()
        }
    }
    
    // Add new tab function
    val addNewTab: () -> Unit = {
        tabs.add(BrowserTab(url = "https://web.tabliss.io/", isNewTab = true))
        currentTabIndex = tabs.size - 1
        showTabsOverview = false
        
        // Show toast message
        Toast.makeText(context, "New tab created", Toast.LENGTH_SHORT).show()
    }
    
    // Close tab function
    val closeTab: (Int) -> Unit = { index ->
        if (tabs.size > 1) {
            val oldCurrentIndex = currentTabIndex
            val closingCurrentTab = index == oldCurrentIndex

            tabs.removeAt(index)

            if (closingCurrentTab) {
                currentTabIndex = if (oldCurrentIndex >= tabs.size) {
                    tabs.size - 1
                } else {
                    oldCurrentIndex
                }
            } else if (index < oldCurrentIndex) {
                currentTabIndex--
            }

            // Mark the new active tab as "switched" to prevent reloading
            if (currentTabIndex < tabs.size) {
                tabs[currentTabIndex].isTabSwitched = true
            }
        } else {
            // If it's the last tab, create a new empty one first
            tabs[0] = BrowserTab()
        }
    }

    // Combined Back Handler Logic
    BackHandler(enabled = true) {
        when {
            showTabsOverview -> showTabsOverview = false
            // Directly check if the current webview can go back
            currentTab?.webView?.canGoBack() == true -> currentTab.webView?.goBack()
            else -> onBackClick() // Fallback to navigate up in the app
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            Column {
                TopAppBar(
                    title = {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .animateContentSize()
                        ) {
                            SearchBar(
                                currentUrl = currentTab?.url ?: "",
                                onSearch = { url -> currentTab?.let { loadUrl(url, currentTabIndex) } },
                                modifier = Modifier.fillMaxWidth(),
                                onFocusChanged = { focused -> 
                                    isUrlBarExpanded = focused
                                }
                            )
                        }
                    },
                    navigationIcon = {
                        // Only show navigation buttons when URL bar is not expanded
                        AnimatedVisibility(
                            visible = !isUrlBarExpanded,
                            enter = fadeIn(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                                    + expandHorizontally(animationSpec = spring(stiffness = Spring.StiffnessMediumLow)),
                            exit = fadeOut(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                                    + shrinkHorizontally(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                        ) {
                            // Navigation area with Close and Home buttons side by side
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Close browser button first
                                IconButton(
                                    onClick = onBackClick,
                                    modifier = Modifier.size(40.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "Close Browser",
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                                
                                // Home button second
                                IconButton(
                                    onClick = { 
                                        // Navigate to homepage
                                        currentTab?.let { loadUrl("https://web.tabliss.io/", currentTabIndex) }
                                    },
                                    modifier = Modifier.size(40.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Home,
                                        contentDescription = "Home",
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                    },
                    actions = {
                        // Only show action buttons when URL bar is not expanded
                        AnimatedVisibility(
                            visible = !isUrlBarExpanded,
                            enter = fadeIn(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                                    + expandHorizontally(animationSpec = spring(stiffness = Spring.StiffnessMediumLow)),
                            exit = fadeOut(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                                    + shrinkHorizontally(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                        ) {
                            Row {
                                // Tab counter button with animation
                                Box(
                                    modifier = Modifier
                                        .size(40.dp)
                                        .clip(CircleShape)
                                        .clickable { showTabsOverview = true }
                                        .background(
                                            MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
                                        )
                                        .border(
                                            width = 1.dp,
                                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.2f),
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = tabs.size.toString(),
                                        style = MaterialTheme.typography.titleMedium.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = MaterialTheme.colorScheme.onSurface
                                    )
                                }
                                
                                // Browser menu button
                                IconButton(
                                    onClick = { showBrowserMenu = true },
                                    modifier = Modifier
                                        .size(40.dp)
                                        .padding(end = 0.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.MoreVert,
                                        contentDescription = "Browser menu",
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                        
                        // Browser menu dropdown
                        DropdownMenu(
                            expanded = showBrowserMenu,
                            onDismissRequest = { showBrowserMenu = false },
                            offset = DpOffset(x = 8.dp, y = 8.dp),
                            modifier = Modifier
                                .width(IntrinsicSize.Min)
                                .padding(0.dp)
                        ) {
                            // Ad blocker toggle
                            DropdownMenuItem(
                                text = { 
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Column(modifier = Modifier.padding(vertical = 4.dp)) {
                                            Text("Block Ads")
                                            if (isAdBlockingEnabled && blockedAdsCount > 0) {
                                                Text(
                                                    text = "$blockedAdsCount ads blocked",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                        Switch(
                                            checked = isAdBlockingEnabled,
                                            onCheckedChange = null
                                        )
                                    }
                                },
                                onClick = {
                                    val newIsEnabled = !isAdBlockingEnabled
                                    coroutineScope.launch {
                                        settingsManager.setAdBlockingEnabled(newIsEnabled)
                                    }
                                    if (newIsEnabled) {
                                        Toast.makeText(context, "Ad blocker enabled", Toast.LENGTH_SHORT).show()
                                    } else {
                                        Toast.makeText(context, "Ad blocker disabled", Toast.LENGTH_SHORT).show()
                                        // Reset stats when disabled
                                        AdBlockStats.reset()
                                    }
                                },
                                modifier = Modifier.height(48.dp),
                                colors = MenuDefaults.itemColors(
                                    textColor = MaterialTheme.colorScheme.onSurface
                                )
                            )
                            
                            Divider(modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp))
                            
                            // Refresh / Stop button
                            DropdownMenuItem(
                                text = { 
                                    Text(
                                        if (isLoading) "Stop loading" else "Refresh page",
                                        style = MaterialTheme.typography.bodyMedium
                                    ) 
                                },
                                onClick = {
                                    if (isLoading) {
                                        currentTab?.webView?.stopLoading()
                                    } else {
                                        currentTab?.let { tab ->
                                            tab.webView?.let { webView ->
                                                // Reset flags that might prevent refresh
                                                tab.isNewTab = false
                                                tab.isTabSwitched = false
                                                
                                                if (tab.url == "https://web.tabliss.io/" || tab.url == "https://web.tabliss.io") {
                                                    webView.clearCache(true)
                                                    webView.reload()
                                                } else {
                                                    val currentUrl = webView.url ?: tab.url
                                                    if (!currentUrl.isNullOrEmpty()) {
                                                        webView.loadUrl(currentUrl, mapOf(
                                                            "Cache-Control" to "no-cache, no-store",
                                                            "Pragma" to "no-cache"
                                                        ))
                                                    } else {
                                                        webView.reload()
                                                    }
                                                }
                                                
                                                isLoading = true
                                                loadingProgress = 0.1f
                                            }
                                        }
                                    }
                                    showBrowserMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = if (isLoading) Icons.Default.Close else Icons.Default.Refresh,
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp)
                                    )
                                },
                                modifier = Modifier.height(40.dp)
                            )
                            
                            // Forward button (only if available)
                            if (canGoForward) {
                                DropdownMenuItem(
                                    text = { 
                                        Text(
                                            "Forward", 
                                            style = MaterialTheme.typography.bodyMedium
                                        ) 
                                    },
                                    onClick = {
                                        currentTab?.webView?.goForward()
                                        showBrowserMenu = false
                                    },
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                                            contentDescription = null,
                                            modifier = Modifier.size(18.dp)
                                        )
                                    },
                                    modifier = Modifier.height(40.dp)
                                )
                            }
                            
                            // New tab button
                            DropdownMenuItem(
                                text = { 
                                    Text(
                                        "New Tab",
                                        style = MaterialTheme.typography.bodyMedium
                                    ) 
                                },
                                onClick = {
                                    addNewTab()
                                    showBrowserMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Add,
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp)
                                    )
                                },
                                modifier = Modifier.height(40.dp)
                            )
                        }
                    }
                )

                // Add the progress bar here
                if (isLoading) {
                    LinearProgressIndicator(
                        progress = loadingProgress,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp),
                        color = MaterialTheme.colorScheme.primary,
                        trackColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                }
            }
        }
    ) { innerPadding ->
        // Content area
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
            // Removed top padding for progress bar as it's now below TopAppBar
        ) {
            // Tabs overview
            if (showTabsOverview) {
                TabsOverviewScreen(
                    tabs = tabs,
                    currentTabIndex = currentTabIndex,
                    onTabSelected = { index ->
                        currentTabIndex = index
                        showTabsOverview = false
                    },
                    onTabClosed = closeTab,
                    onAddTab = addNewTab
                )
            } else {
                // WebView container - Check if tabs list is not empty before accessing
                if (tabs.isNotEmpty() && currentTabIndex < tabs.size) {
                    val tab = tabs[currentTabIndex]
                    
                    Box(modifier = Modifier.fillMaxSize()) {
                        // Show error view if there's an error
                        if (tab.errorState != null) {
                            ErrorView(
                                error = tab.errorState!!,
                                onRetry = {
                                    tab.errorState = null
                                    tab.webView?.reload()
                                }
                            )
                        } else {
                            AndroidView(
                                factory = { ctx ->
                                    // Reuse existing WebView if available, or create a new one
                                    tab.webView ?: WebView(ctx).apply {
                                        layoutParams = ViewGroup.LayoutParams(
                                            ViewGroup.LayoutParams.MATCH_PARENT,
                                            ViewGroup.LayoutParams.MATCH_PARENT
                                        )
                                        
                                        // Configure WebView settings
                                        settings.apply {
                                            javaScriptEnabled = true
                                            domStorageEnabled = true
                                            databaseEnabled = true
                                            loadWithOverviewMode = true
                                            useWideViewPort = true
                                            setSupportZoom(true)
                                            builtInZoomControls = true
                                            displayZoomControls = false
                                            javaScriptCanOpenWindowsAutomatically = true
                                            mediaPlaybackRequiresUserGesture = false
                                            setGeolocationEnabled(true)
                                            mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
                                            setSupportMultipleWindows(true)
                                            
                                            // Performance optimizations
                                            setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                                            
                                            // Optimize caching for faster loads
                                            cacheMode = android.webkit.WebSettings.LOAD_DEFAULT
                                            databaseEnabled = true
                                            domStorageEnabled = true
                                            
                                            // Additional performance optimizations
                                            allowContentAccess = true
                                            blockNetworkImage = false // Set to true for faster initial load, but images won't load
                                            loadsImagesAutomatically = true
                                            
                                            // Reduce resource usage
                                            setNeedInitialFocus(false)
                                            
                                            // Use page scaling instead of initialScale
                                            setLoadWithOverviewMode(true)
                                            setUseWideViewPort(true)
                                        }
                                        
                                        // Enable hardware acceleration at the view level with additional optimization
                                        setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                                        isHorizontalScrollBarEnabled = false
                                        isVerticalScrollBarEnabled = false
                                        scrollBarStyle = android.view.View.SCROLLBARS_INSIDE_OVERLAY
                                        overScrollMode = android.view.View.OVER_SCROLL_NEVER
                                        
                                        webViewClient = object : WebViewClient() {
                                            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                                                super.onPageStarted(view, url, favicon)
                                                isLoading = true
                                                tab.errorState = null // Clear any previous errors
                                                url?.let {
                                                    tab.url = it
                                                }
                                                canGoBack = view?.canGoBack() ?: false
                                                canGoForward = view?.canGoForward() ?: false
                                            }
                                            
                                            override fun shouldInterceptRequest(
                                                view: WebView?,
                                                request: WebResourceRequest?
                                            ): WebResourceResponse? {
                                                // Check if this is a download request
                                                val requestUrl = request?.url?.toString() ?: return null
                                                val headers = request.requestHeaders ?: emptyMap()
                                                
                                                // If Content-Disposition header suggests a download, let the DownloadListener handle it
                                                if (headers["Content-Disposition"]?.contains("attachment") == true) {
                                                    return null
                                                }
                                                
                                                // Process with ad blocker
                                                return request?.let { adBlockManager.processRequest(it) }
                                                    ?: super.shouldInterceptRequest(view, request)
                                            }
                                            
                                            override fun onPageFinished(view: WebView?, url: String?) {
                                                super.onPageFinished(view, url)
                                                isLoading = false
                                                loadingProgress = 1f
                                                url?.let { tab.url = it }
                                                canGoBack = view?.canGoBack() ?: false
                                                canGoForward = view?.canGoForward() ?: false
                                                
                                                view?.title?.let { title ->
                                                    if (title.isNotBlank()) {
                                                        tab.title = title
                                                    }
                                                }
                                                
                                                // Inject enhanced ad-blocking JavaScript
                                                view?.let { webView ->
                                                    val enhancedAdBlockScript = """
                                                        (function() {
                                                            // Create and append style to block common ad elements
                                                            var style = document.createElement('style');
                                                            style.type = 'text/css';
                                                            style.innerHTML = `
                                                                /* Ad containers and common ad related elements */
                                                                div[id*="google_ads_iframe"],
                                                                div[id^="div-gpt-ad"],
                                                                div[data-ad-slot],
                                                                div[id^="adUnit"],
                                                                div[id*="-ad-"],
                                                                div[class*="-ad-container"],
                                                                div[class^="adthrive"],
                                                                div[id^="adthrive"],
                                                                iframe[id*="google_ads_iframe"],
                                                                iframe[src*="doubleclick.net"],
                                                                iframe[src*="/ad/"],
                                                                iframe[src*="/ads/"],
                                                                div[class*="ad-container"]:not(.site-logo):not(.logo):not(.header-logo),
                                                                div[class*="ad-wrapper"]:not(.site-logo):not(.logo):not(.header-logo),
                                                                div[class*="advert"]:not(.site-logo):not(.logo):not(.header-logo),
                                                                #ad-footer:not(.site-footer):not(.footer),
                                                                #ad-header:not(.site-header):not(.header),
                                                                
                                                                /* Additional ad targeting */
                                                                div[id*="banner-ad"],
                                                                div[class*="banner-ad"],
                                                                div[id*="sticky-ad"],
                                                                div[class*="sticky-ad"],
                                                                div[id*="overlay-ad"],
                                                                div[class*="overlay-ad"],
                                                                div[class*="adsbox"],
                                                                div[id*="adsbox"],
                                                                div[class*="adsbygoogle"],
                                                                div[id*="adsbygoogle"],
                                                                ins.adsbygoogle,
                                                                [id*="taboola"],
                                                                [class*="taboola"],
                                                                [id*="outbrain"],
                                                                [class*="outbrain"],
                                                                [id*="mgid"],
                                                                [class*="mgid"],
                                                                [id*="zergnet"],
                                                                [class*="zergnet"],
                                                                [class*="AdContainer"],
                                                                [class*="ad-unit"],
                                                                [class*="ad-slot"],
                                                                [data-ad-client],
                                                                [data-ad-channel],
                                                                
                                                                /* Social media embeds that often contain ads */
                                                                div[id*="facebook-ad"],
                                                                div[class*="facebook-ad"],
                                                                div[id*="twitter-ad"],
                                                                div[class*="twitter-ad"],
                                                                
                                                                /* Popup and overlay containers - Disabled for better compatibility with shortlinks
                                                                div[id*="popup"],
                                                                div[class*="popup"]:not(.popup-menu):not(.popup-content),
                                                                div[id*="modal"]:not(.modal-content):not(.modal-dialog),
                                                                div[class*="modal"]:not(.modal-content):not(.modal-dialog),
                                                                div[id*="overlay"]:not(.overlay-content),
                                                                div[class*="overlay"]:not(.overlay-content),
                                                                div[id*="lightbox"]:not(.lightbox-content),
                                                                div[class*="lightbox"]:not(.lightbox-content),
                                                                */
                                                                
                                                                /* Newsletter and subscription popups */
                                                                div[id*="subscribe"],
                                                                div[class*="subscribe"],
                                                                div[id*="newsletter"],
                                                                div[class*="newsletter"],
                                                                
                                                                /* Cookie banners and consent notices */
                                                                #cookie-banner,
                                                                #cookie-consent,
                                                                #cookie-notice,
                                                                .cookie-banner,
                                                                .cookie-consent,
                                                                .cookie-notice,
                                                                div[class*="cookie-banner"],
                                                                div[class*="cookie-consent"],
                                                                div[class*="cookie-notice"],
                                                                div[id*="gdpr"],
                                                                div[class*="gdpr"],
                                                                div[id*="consent"],
                                                                div[class*="consent-banner"],
                                                                
                                                                /* Fixed positioned elements likely to be ads or notices */
                                                                div[class*="fixed-banner"],
                                                                div[class*="fixed-ad"],
                                                                div[class*="sticky-banner"],
                                                                div[class*="float-banner"],
                                                                div[class*="float-ad"],
                                                                div[id*="adhesion"],
                                                                div[class*="adhesion"],
                                                                div[id*="anchor-ad"],
                                                                div[class*="anchor-ad"] {
                                                                    display: none !important;
                                                                    opacity: 0 !important;
                                                                    pointer-events: none !important;
                                                                    visibility: hidden !important;
                                                                    height: 0 !important;
                                                                    width: 0 !important;
                                                                    max-height: 0 !important;
                                                                    max-width: 0 !important;
                                                                    overflow: hidden !important;
                                                                    position: absolute !important;
                                                                    left: -9999px !important;
                                                                    z-index: -999 !important;
                                                                }
                                                                
                                                                /* Preserve logos and essential page elements */
                                                                .logo, .site-logo, .header-logo, .brand, .brand-logo,
                                                                header img, .navbar-brand img, .site-header img,
                                                                .site-navigation, .main-navigation, .main-content,
                                                                main, article, .article-content, .entry-content,
                                                                .post-content, .page-content {
                                                                    display: initial !important;
                                                                    opacity: initial !important;
                                                                    pointer-events: initial !important;
                                                                    height: initial !important;
                                                                    width: initial !important;
                                                                    max-height: initial !important;
                                                                    max-width: initial !important;
                                                                    visibility: initial !important;
                                                                    position: initial !important;
                                                                    z-index: initial !important;
                                                                }
                                                            `;
                                                            document.head.appendChild(style);
                                                            
                                                            // Block ad-related JavaScript functions
                                                            try {
                                                                // Override common ad function calls
                                                                window.googletag = { cmd: [], pubads: function() { return { refresh: function() {}, addEventListener: function() {}, setTargeting: function() {} }; } };
                                                                window.ga = function() {};
                                                                window.__ga__ = function() {};
                                                                window.gtag = function() {};
                                                                window.fbq = function() {};
                                                                window._comscore = [];
                                                                window.__INITIAL_STATE__ = window.__INITIAL_STATE__ || {}; 
                                                                window.__INITIAL_STATE__.ads = { slots: [] };
                                                                
                                                                // Prevent common advertiser's fingerprinting techniques
                                                                if (Navigator.prototype.hasOwnProperty('brave')) {
                                                                    Object.defineProperty(Navigator.prototype, 'brave', { get: function() { return undefined; } });
                                                                }
                                                                
                                                                // Allow window.open calls to work normally (they will be handled by onCreateWindow)
                                                                // Only block obvious spam/malicious popups
                                                                var originalOpen = window.open;
                                                                window.open = function(url) {
                                                                    // Block only obvious spam/malicious popups
                                                                    if (url && (url.includes('malware') || url.includes('virus') ||
                                                                        url.includes('scam') || url.includes('phishing'))) {
                                                                        console.log('Blocked malicious popup:', url);
                                                                        return null;
                                                                    }

                                                                    // Allow all other window.open calls to proceed normally
                                                                    return originalOpen.apply(this, arguments);
                                                                };
                                                                
                                                                // Block event listeners related to ads and tracking
                                                                var originalAddEventListener = EventTarget.prototype.addEventListener;
                                                                EventTarget.prototype.addEventListener = function(type, listener, options) {
                                                                    if (type === 'adblockDetected' || type === 'load' && listener && listener.toString && 
                                                                       (listener.toString().includes('ad') || listener.toString().includes('track'))) {
                                                                        return; // Block ad related event listeners
                                                                    }
                                                                    
                                                                    return originalAddEventListener.apply(this, arguments);
                                                                };
                                                            } catch (e) {
                                                                console.error('Ad blocking script error:', e);
                                                            }
                                                            
                                                            // Periodically remove ad elements that might be dynamically added
                                                            var adElementChecker = function() {
                                                                try {
                                                                    // More specific ad selectors
                                                                    var adSelectors = [
                                                                        // Ad iframes and containers
                                                                        '[id*="google_ads"]', 
                                                                        '[id*="div-gpt-ad"]',
                                                                        '[data-ad-slot]',
                                                                        '[class*="adsbygoogle"]',
                                                                        '[id*="adsbygoogle"]',
                                                                        'ins.adsbygoogle',
                                                                        'iframe[src*="doubleclick.net"]',
                                                                        'iframe[src*="ad."]',
                                                                        'iframe[src*="/ad"]',
                                                                        'iframe[src*="ads."]',
                                                                        'iframe[id*="ad-"]',
                                                                        'iframe[id*="Ad"]',
                                                                        '[class*="-ad-placeholders"]',
                                                                        '[id*="banner-ad"]',
                                                                        '[class*="ad-container"]',
                                                                        
                                                                        /* Popups and overlays - Disabled for better compatibility with shortlinks
                                                                        '.modal:not(.modal-content):not(.modal-dialog)',
                                                                        '.overlay:not(.overlay-content)',
                                                                        '.popup:not(.popup-menu):not(.popup-content)',
                                                                        */
                                                                        
                                                                        // Fixed position elements likely to be ads
                                                                        '.sticky-ad',
                                                                        '.fixed-ad',
                                                                        '.adhesion-ad',
                                                                        '.anchor-ad'
                                                                    ];
                                                                    
                                                                    // Combined selector for better performance
                                                                    var combinedSelector = adSelectors.join(', ');
                                                                    var adElements = document.querySelectorAll(combinedSelector);
                                                                    
                                                                    for (var i = 0; i < adElements.length; i++) {
                                                                        var elem = adElements[i];
                                                                        
                                                                        // Skip essential elements
                                                                        if (elem.classList.contains('logo') || 
                                                                            elem.classList.contains('site-logo') || 
                                                                            elem.classList.contains('brand-logo') ||
                                                                            elem.closest('header') || 
                                                                            elem.closest('nav')) {
                                                                            continue;
                                                                        }
                                                                        
                                                                        // Hide the element
                                                                        elem.style.setProperty('display', 'none', 'important');
                                                                        elem.style.setProperty('visibility', 'hidden', 'important');
                                                                        elem.style.setProperty('opacity', '0', 'important');
                                                                        elem.style.setProperty('pointer-events', 'none', 'important');
                                                                        
                                                                        // Try to prevent reflow by collapsing
                                                                        elem.style.setProperty('height', '0', 'important');
                                                                        elem.style.setProperty('width', '0', 'important');
                                                                        elem.style.setProperty('overflow', 'hidden', 'important');
                                                                        
                                                                        // Prevent the element from taking space
                                                                        elem.style.setProperty('position', 'absolute', 'important');
                                                                        elem.style.setProperty('left', '-9999px', 'important');
                                                                    }
                                                                    
                                                                    // Detect anti-adblock dialogs
                                                                    var antiAdBlockSelectors = [
                                                                        '[class*="adblock"]',
                                                                        '[id*="adblock"]',
                                                                        '[class*="AdBlock"]',
                                                                        '[id*="AdBlock"]',
                                                                        '.blocker',
                                                                        '.ad-blocker-warning',
                                                                        '.ad-blocked',
                                                                        '[class*="disabled-ad"]',
                                                                        '.adblock-notice',
                                                                        '.antiadblock',
                                                                        '.ad-blockers',
                                                                        '.ad-block-detected'
                                                                    ];
                                                                    
                                                                    var antiAdBlockElements = document.querySelectorAll(antiAdBlockSelectors.join(', '));
                                                                    for (var j = 0; j < antiAdBlockElements.length; j++) {
                                                                        var element = antiAdBlockElements[j];
                                                                        if (element.textContent && (
                                                                            element.textContent.toLowerCase().includes('adblock') ||
                                                                            element.textContent.toLowerCase().includes('ad block') ||
                                                                            element.textContent.toLowerCase().includes('disable your') ||
                                                                            element.textContent.toLowerCase().includes('whitelist'))) {
                                                                            
                                                                            element.style.setProperty('display', 'none', 'important');
                                                                            element.style.setProperty('visibility', 'hidden', 'important');
                                                                        }
                                                                    }
                                                                    
                                                                } catch (e) {
                                                                    console.error('Ad element checker error:', e);
                                                                }
                                                            };
                                                            
                                                            // Run immediately
                                                            adElementChecker();
                                                            
                                                            // Run periodically to catch new ads - less frequently for better performance
                                                            var adElementInterval = setInterval(adElementChecker, 2000);
                                                            
                                                            // Clear interval after 60 seconds to avoid ongoing performance impact
                                                            setTimeout(function() {
                                                                clearInterval(adElementInterval);
                                                                
                                                                // Continue with less frequent checks
                                                                setInterval(adElementChecker, 10000);
                                                            }, 60000);
                                                            
                                                            // Clean up after 5 minutes to prevent memory leaks
                                                            setTimeout(function() {
                                                                clearInterval(adElementInterval);
                                                            }, 300000);
                                                        })();
                                                    """.trimIndent()
                                                    
                                                    webView.evaluateJavascript(enhancedAdBlockScript, null)
                                                }
                                            }
                                            
                                            @Deprecated("Deprecated in Java")
                                            override fun onReceivedError(
                                                view: WebView?,
                                                errorCode: Int,
                                                description: String?,
                                                failingUrl: String?
                                            ) {
                                                super.onReceivedError(view, errorCode, description, failingUrl)
                                                isLoading = false
                                                loadingProgress = 1f
                                                
                                                // Create error state without toast
                                                val errorMessage = when (errorCode) {
                                                    ERROR_HOST_LOOKUP -> "Cannot find the website. Check your internet connection."
                                                    ERROR_TIMEOUT -> "Connection timed out. Please try again."
                                                    ERROR_CONNECT -> "Failed to connect to the server."
                                                    ERROR_UNKNOWN -> "Unknown error occurred."
                                                    -10 -> "URL scheme not supported. The link may require a specific app to open."
                                                    else -> description ?: "An error occurred"
                                                }
                                                
                                                tab.errorState = WebViewError(errorCode, errorMessage, failingUrl)
                                            }
                                            
                                            // Add support for modern error callback
                                            @SuppressLint("NewApi")
                                            override fun onReceivedError(
                                                view: WebView?, 
                                                request: WebResourceRequest?, 
                                                error: android.webkit.WebResourceError?
                                            ) {
                                                // Only handle main frame errors, not resource errors
                                                if (request?.isForMainFrame == true) {
                                                    error?.let {
                                                        onReceivedError(
                                                            view,
                                                            it.errorCode,
                                                            it.description?.toString(),
                                                            request.url.toString()
                                                        )
                                                    }
                                                }
                                            }
                                            
                                            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                                                return handleUrlScheme(request?.url?.toString(), context)
                                            }
                                        }
                                        
                                        webChromeClient = object : WebChromeClient() {
                                            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                                                loadingProgress = newProgress / 100f
                                                if (newProgress == 100) {
                                                    isLoading = false
                                                } else {
                                                    isLoading = true
                                                }
                                            }
                                            
                                            override fun onReceivedTitle(view: WebView?, title: String?) {
                                                super.onReceivedTitle(view, title)
                                                title?.let {
                                                    if (it.isNotBlank()) {
                                                        tab.title = it
                                                    }
                                                }
                                            }
                                            
                                            override fun onReceivedIcon(view: WebView?, icon: Bitmap?) {
                                                super.onReceivedIcon(view, icon)
                                                icon?.let { tab.favicon = it }
                                            }

                                            // Intercept requests to create new windows (popups) and open them in new tabs
                                            override fun onCreateWindow(
                                                view: WebView?,
                                                isDialog: Boolean,
                                                isUserGesture: Boolean,
                                                resultMsg: android.os.Message?
                                            ): Boolean {
                                                if (resultMsg == null) {
                                                    return false
                                                }

                                                // Create a new tab for the popup
                                                val newTab = BrowserTab(url = "about:blank", title = "Loading...")
                                                tabs.add(newTab)
                                                val newTabIndex = tabs.size - 1

                                                // Create a new WebView for the popup that will be used in the new tab
                                                val popupWebView = WebView(context).apply {
                                                    layoutParams = ViewGroup.LayoutParams(
                                                        ViewGroup.LayoutParams.MATCH_PARENT,
                                                        ViewGroup.LayoutParams.MATCH_PARENT
                                                    )

                                                    // Configure WebView settings (same as main WebView)
                                                    settings.apply {
                                                        javaScriptEnabled = true
                                                        domStorageEnabled = true
                                                        databaseEnabled = true
                                                        loadWithOverviewMode = true
                                                        useWideViewPort = true
                                                        setSupportZoom(true)
                                                        builtInZoomControls = true
                                                        displayZoomControls = false
                                                        javaScriptCanOpenWindowsAutomatically = true
                                                        mediaPlaybackRequiresUserGesture = false
                                                        setGeolocationEnabled(true)
                                                        mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
                                                        setSupportMultipleWindows(true)
                                                        cacheMode = android.webkit.WebSettings.LOAD_DEFAULT
                                                        allowContentAccess = true
                                                        blockNetworkImage = false
                                                        loadsImagesAutomatically = true
                                                        setNeedInitialFocus(false)
                                                        setLoadWithOverviewMode(true)
                                                        setUseWideViewPort(true)
                                                    }


                                                    // Set up WebViewClient for the popup
                                                    webViewClient = object : WebViewClient() {
                                                        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                                                            super.onPageStarted(view, url, favicon)
                                                            url?.let { newTab.url = it }
                                                        }

                                                        override fun onPageFinished(view: WebView?, url: String?) {
                                                            super.onPageFinished(view, url)
                                                            url?.let { newTab.url = it }
                                                            view?.title?.let { title ->
                                                                if (title.isNotBlank()) {
                                                                    newTab.title = title
                                                                }
                                                            }
                                                        }

                                                        override fun shouldInterceptRequest(
                                                            view: WebView?,
                                                            request: WebResourceRequest?
                                                        ): WebResourceResponse? {
                                                            // Process with ad blocker
                                                            return request?.let { adBlockManager.processRequest(it) }
                                                                ?: super.shouldInterceptRequest(view, request)
                                                        }
                                                    }

                                                    // Set up WebChromeClient for the popup
                                                    webChromeClient = object : WebChromeClient() {
                                                        override fun onReceivedTitle(view: WebView?, title: String?) {
                                                            super.onReceivedTitle(view, title)
                                                            title?.let {
                                                                if (it.isNotBlank()) {
                                                                    newTab.title = it
                                                                }
                                                            }
                                                        }

                                                        override fun onReceivedIcon(view: WebView?, icon: Bitmap?) {
                                                            super.onReceivedIcon(view, icon)
                                                            icon?.let { newTab.favicon = it }
                                                        }

                                                        override fun onCloseWindow(window: WebView?) {
                                                            // Close the tab when the popup requests to close
                                                            val tabIndex = tabs.indexOf(newTab)
                                                            if (tabIndex != -1) {
                                                                closeTab(tabIndex)
                                                            }
                                                        }
                                                    }
                                                }

                                                // Store the WebView in the new tab
                                                newTab.webView = popupWebView

                                                // The transport object is the bridge to the new WebView
                                                val transport = resultMsg.obj as WebView.WebViewTransport
                                                transport.webView = popupWebView
                                                resultMsg.sendToTarget()

                                                // Switch to the new tab automatically
                                                currentTabIndex = newTabIndex

                                                // Show a toast to inform user about the new tab
                                                Toast.makeText(context, "Popup opened in new tab", Toast.LENGTH_SHORT).show()

                                                return true
                                            }
                                        }
                                        
                                        // Download interceptor
                                        setDownloadListener { url, _, contentDisposition, mimetype, _ ->
                                            // Prevent multiple simultaneous download dialogs 
                                            if (isProcessingDownload) return@setDownloadListener
                                            
                                            isProcessingDownload = true
                                            
                                            // Try to get the filename from Content-Disposition header first
                                            val fileName = when {
                                                contentDisposition != null -> {
                                                    // Try different patterns for Content-Disposition
                                                    val patterns = listOf(
                                                        "filename=[\"]?([^\"]*)",  // Standard format
                                                        "filename\\*=UTF-8''([^;]*)",  // RFC 5987 encoded
                                                        "filename\\*=([^;]*)",  // RFC 5987 without charset
                                                        "name=[\"]?([^\"]*)"  // Alternative format
                                                    )
                                                    
                                                    var extractedName: String? = null
                                                    for (pattern in patterns) {
                                                        val matcher = java.util.regex.Pattern.compile(pattern).matcher(contentDisposition)
                                                        if (matcher.find()) {
                                                            extractedName = matcher.group(1)
                                                            // If it's URL encoded, decode it
                                                            if (pattern.contains("filename\\*")) {
                                                                try {
                                                                    extractedName = java.net.URLDecoder.decode(
                                                                        extractedName,
                                                                        StandardCharsets.UTF_8.name()
                                                                    )
                                                                } catch (e: Exception) {
                                                                    // If decoding fails, use the original
                                                                }
                                                            }
                                                            break
                                                        }
                                                    }
                                                    
                                                    extractedName
                                                }
                                                else -> null
                                            } ?: run {
                                                // Fallback to URL-based filename with MIME type
                                                val mimeTypeMap = android.webkit.MimeTypeMap.getSingleton()
                                                val extension = when {
                                                    !mimetype.isNullOrBlank() -> 
                                                        mimeTypeMap.getExtensionFromMimeType(mimetype)
                                                    else -> {
                                                        try {
                                                            val path = java.net.URL(url).path
                                                            val lastDotIndex = path.lastIndexOf('.')
                                                            if (lastDotIndex > 0) {
                                                                path.substring(lastDotIndex + 1)
                                                            } else {
                                                                null
                                                            }
                                                        } catch (e: Exception) {
                                                            null
                                                        }
                                                    }
                                                }
                                                
                                                val baseName = try {
                                                    val path = java.net.URL(url).path
                                                    path.substring(path.lastIndexOf('/') + 1)
                                                        .substringBeforeLast('.')
                                                        .takeIf { it.isNotEmpty() } ?: "download"
                                                } catch (e: Exception) {
                                                    "download"
                                                }
                                                
                                                "$baseName.${extension ?: "bin"}"
                                            }
                                            
                                            // Set the URL in state and show dialog
                                            interceptedDownloadUrl = url
                                            showAddDownloadDialog = true
                                        }
                                        
                                        // Load initial URL if not already loaded
                                        if (tab.url.isNotBlank()) {
                                            loadUrl(tab.url, currentTabIndex)
                                        }
                                    }.also { webView ->
                                        // Store the WebView in the tab object
                                        tab.webView = webView
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxSize()
                                    // Use a simpler GPU rendering hint
                                    .graphicsLayer {
                                        // Force hardware acceleration with minimal alpha change
                                        // This creates a new render layer without visible changes
                                        alpha = 0.99f
                                    },
                                update = { webView ->
                                    // The update block is called on recomposition.
                                    // We must be careful not to reload the URL here, as that would
                                    // cause refreshes when switching tabs. The WebView's state
                                    // is the source of truth for its current URL.

                                    // Reset flags after the view is shown to prevent reload loops.
                                    if (tab.isNewTab) {
                                        tab.isNewTab = false
                                    }
                                    if (tab.isTabSwitched) {
                                        tab.isTabSwitched = false
                                    }
                                    
                                    // Ensure the webView reference in our tab state is always current.
                                    tab.webView = webView
                                }
                            )
                        }
                    }
                } else if (tabs.isEmpty()) {
                    // Optional: Show a placeholder if there are no tabs
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        Text("No tabs open")
                    }
                }
            }
        }
    }
    
    // Add Download Dialog
    if (showAddDownloadDialog) {
        val focusRequester = remember { androidx.compose.ui.focus.FocusRequester() }
        
        LaunchedEffect(showAddDownloadDialog) {
            // Give time for the WebView to stabilize and prevent rapid dialog reappearance
            delay(150)
            focusRequester.requestFocus()
        }
        
        AlertDialog(
            onDismissRequest = { 
                showAddDownloadDialog = false
                isProcessingDownload = false
            },
            containerColor = MaterialTheme.colorScheme.surface,
            shape = RoundedCornerShape(28.dp),
            title = { Text("Add Download", style = MaterialTheme.typography.headlineSmall) },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text("Would you like to download this file?")
                    
                    OutlinedTextField(
                        value = interceptedDownloadUrl,
                        onValueChange = { interceptedDownloadUrl = it },
                        label = { Text("URL") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            unfocusedBorderColor = MaterialTheme.colorScheme.outline
                        ),
                        shape = RoundedCornerShape(16.dp),
                        singleLine = true,
                        readOnly = false
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        if (interceptedDownloadUrl.isNotBlank()) {
                            // Process URL
                            val url = interceptedDownloadUrl
                            // Add download using viewModel
                            viewModel.addDownload(url)
                            showAddDownloadDialog = false
                            isProcessingDownload = false
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text("Download")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showAddDownloadDialog = false 
                        isProcessingDownload = false
                    },
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun TabsOverviewScreen(
    tabs: List<BrowserTab>,
    currentTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    onTabClosed: (Int) -> Unit,
    onAddTab: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp)
    ) {
        // Header with title and tab count
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Tabs (${tabs.size})",
                style = MaterialTheme.typography.titleLarge
            )
            
            // Add new tab button in header
            FilledTonalIconButton(
                onClick = onAddTab,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "New tab",
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // Tabs grid with LazyVerticalGrid
        androidx.compose.foundation.lazy.grid.LazyVerticalGrid(
            columns = androidx.compose.foundation.lazy.grid.GridCells.Adaptive(minSize = 160.dp),
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(tabs.size) { index ->
                TabCard(
                    tab = tabs[index],
                    isSelected = index == currentTabIndex,
                    onSelect = { 
                        // Mark tab as switched to prevent reload
                        if (index != currentTabIndex && index < tabs.size) {
                            tabs[index].isTabSwitched = true
                        }
                        onTabSelected(index) 
                    },
                    onClose = { onTabClosed(index) }
                )
            }
        }
    }
}

@Composable
private fun TabCard(
    tab: BrowserTab,
    isSelected: Boolean,
    onSelect: () -> Unit,
    onClose: () -> Unit
) {
    val elevation by animateDpAsState(
        targetValue = if (isSelected) 8.dp else 1.dp,
        label = "Card Elevation"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
            .clip(RoundedCornerShape(16.dp))
            .clickable(onClick = onSelect),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.secondaryContainer
            else
                MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp)
        ) {
            // Header with favicon, close button, and new tab indicator
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Favicon or initial
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)),
                    contentAlignment = Alignment.Center
                ) {
                    if (tab.favicon != null) {
                        Image(
                            bitmap = tab.favicon!!.asImageBitmap(),
                            contentDescription = "Favicon",
                            modifier = Modifier
                                .size(24.dp)
                                .clip(RoundedCornerShape(4.dp))
                        )
                    } else {
                        Text(
                            text = tab.title.firstOrNull()?.uppercaseChar()?.toString() ?: "T",
                            style = MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.Bold),
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }

                // Add the new tab indicator
                if (isSelected) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = MaterialTheme.colorScheme.primary,
                                shape = CircleShape
                            )
                    )
                }

                // Close button
                IconButton(
                    onClick = onClose,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close tab",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Tab title
            Text(
                text = tab.title,
                style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Medium),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )
            
            // URL
            Text(
                text = tab.url,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

@Composable
private fun ErrorView(
    error: WebViewError,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.ErrorOutline,
            contentDescription = "Error",
            modifier = Modifier
                .size(64.dp)
                .padding(bottom = 16.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Text(
            text = "Something went wrong",
            style = MaterialTheme.typography.titleLarge,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = error.description ?: "Failed to load page",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        Button(
            onClick = onRetry,
            modifier = Modifier.padding(top = 16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Try Again")
        }
    }
}

@Composable
private fun SearchBar(
    currentUrl: String,
    onSearch: (String) -> Unit,
    modifier: Modifier = Modifier,
    onFocusChanged: (Boolean) -> Unit = {}
) {
    // Don't show the URL if it's the default homepage
    val initialText = if (currentUrl == "https://web.tabliss.io/" || 
                         currentUrl == "https://web.tabliss.io") {
        ""
    } else {
        currentUrl
    }
    
    var text by remember { mutableStateOf(initialText) }
    var isEditing by remember { mutableStateOf(false) }
    val focusManager = LocalFocusManager.current
    val interactionSource = remember { MutableInteractionSource() }

    // Track theme settings
    val context = LocalContext.current
    val settingsManager = remember { SettingsManager(context) }
    val isAmoledDarkMode by settingsManager.isAmoledDarkMode.collectAsState(initial = false)
    val isDarkTheme = MaterialTheme.colorScheme.background.luminance() < 0.5

    // Animation for expanding the search bar when focused
    val heightAnimation by animateDpAsState(
        targetValue = if (isEditing) 52.dp else 40.dp,
        label = "Search Bar Height",
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )
    
    val paddingAnimation by animateDpAsState(
        targetValue = if (isEditing) 0.dp else 8.dp,
        label = "Search Bar Padding",
        animationSpec = tween(durationMillis = 200)
    )
    
    // Remember focus requester to control focus programmatically
    val focusRequester = remember { FocusRequester() }
    
    // Manage selection on focus change
    LaunchedEffect(isEditing) {
        if (isEditing) {
            // Small delay to ensure the text field is ready for selection
            delay(100)
            android.util.Log.d("SearchBar", "Should select all text: $text")
            // This log helps debugging but doesn't actually select the text
            // Selection needs to be handled by the platform
        }
        // Notify parent about focus change
        onFocusChanged(isEditing)
    }

    // Update text when URL changes and it's not the default homepage
    LaunchedEffect(currentUrl) {
        if (currentUrl != "https://web.tabliss.io/" && 
            currentUrl != "https://web.tabliss.io" && 
            !isEditing) {
            text = currentUrl
        }
    }

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(heightAnimation)
            .padding(horizontal = paddingAnimation)
            .clip(RoundedCornerShape(20.dp))
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f),
                shape = RoundedCornerShape(20.dp)
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(bounded = true, color = MaterialTheme.colorScheme.primary),
                onClick = {
                    // Focus the text field when the URL bar is clicked
                    focusRequester.requestFocus()
                }
            ),
        color = getUrlBarBackgroundColor(),
        shape = RoundedCornerShape(20.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Use a more Chrome-like search icon
            Icon(
                imageVector = if (isEditing) Icons.Default.Search else Icons.Default.Public,
                contentDescription = "Search",
                tint = getIconTint(MaterialTheme.colorScheme.onSurfaceVariant),
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            BasicTextField(
                value = text,
                onValueChange = { text = it },
                modifier = Modifier
                    .weight(1f)
                    .focusRequester(focusRequester)
                    .onFocusChanged { 
                        val wasPreviouslyEditing = isEditing
                        isEditing = it.isFocused
                        
                        // When gaining focus and not previously editing, perform URL selection
                        if (isEditing && !wasPreviouslyEditing) {
                            android.util.Log.d("SearchBar", "Gained focus, should select URL")
                        }
                    },
                textStyle = MaterialTheme.typography.bodyMedium.copy(
                    color = getIconTint(MaterialTheme.colorScheme.onSurface)
                ),
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Search
                ),
                keyboardActions = KeyboardActions(
                    onSearch = {
                        onSearch(text)
                        focusManager.clearFocus()
                    }
                ),
                interactionSource = interactionSource
            )

            if (text.isNotEmpty() && isEditing) {
                IconButton(
                    onClick = { text = "" },
                    modifier = Modifier
                        .size(30.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.0f))
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.0f),
                            shape = CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Clear search",
                        tint = getIconTint(MaterialTheme.colorScheme.onSurfaceVariant),
                        modifier = Modifier.size(25.dp)
                    )
                }
            }
        }
    }
}
